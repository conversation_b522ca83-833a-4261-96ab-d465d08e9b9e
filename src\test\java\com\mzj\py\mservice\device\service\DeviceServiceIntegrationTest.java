package com.mzj.py.mservice.device.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.enums.BindStatusEnum;
import com.mzj.py.commons.exception.ServiceException;
import com.mzj.py.mservice.device.vo.*;
import com.mzj.py.mservice.home.entity.*;
import com.mzj.py.mservice.home.repository.*;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.service.StoreService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DeviceService集成测试类
 * 测试复杂业务场景和边界情况
 */
@ExtendWith(MockitoExtension.class)
class DeviceServiceIntegrationTest {

    @InjectMocks
    private DeviceService deviceService;

    @Mock
    private JdbcTemplate jdbcTemplate;
    @Mock
    private DeviceRepository deviceRepository;
    @Mock
    private DeviceVoiceRepository deviceVoiceRepository;
    @Mock
    private OSSService ossService;
    @Mock
    private StoreService shopService;
    @Mock
    private ShopRepository shopRepository;
    @Mock
    private ShopUserRefRepository shopUserRefRepository;
    @Mock
    private RemoteDeviceService remoteDeviceService;
    @Mock
    private VoicePacketRepository voicePacketRepository;
    @Mock
    private VoiceWorkRepository workRepository;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(deviceService, "cdnUrl", "https://test-oss.com/");
    }

    @Test
    void 测试设备完整生命周期_创建绑定添加语音解绑() {
        // 测试设备的完整生命周期：创建 -> 绑定 -> 添加语音 -> 解绑

        // 1. 创建设备
        Device newDevice = createTestDevice();
        ShopUserRef shopRef = new ShopUserRef();
        shopRef.setShopId(1L);
        shopRef.setUserId(1L);

        when(shopService.createShop(1L)).thenReturn(shopRef);
        when(deviceRepository.save(any(Device.class))).thenReturn(newDevice);

        ResultBean<Boolean> createResult = deviceService.addOrUpdate(newDevice);
        assertTrue(createResult.isOk());

        // 使设备处于未绑定状态（shopId 为空），以验证绑定逻辑只能处理未绑定设备
        newDevice.setShopId(null);

        // 2. 绑定设备到门店
        DeviceUnBindVo bindVo = new DeviceUnBindVo();
        bindVo.setId(1L);
        bindVo.setShopId(1L);
        bindVo.setShopIds(Arrays.asList(1L, 2L)); // 设置shopIds避免NullPointerException
        bindVo.setUserId(1L); // 设置userId

        when(deviceRepository.existsById(1L)).thenReturn(true);
        when(shopRepository.existsById(1L)).thenReturn(true);
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(newDevice));

        ResultBean<Boolean> bindResult = deviceService.bind(bindVo);
        assertTrue(bindResult.isOk());

        // 3. 添加语音到设备
        DeviceVoiceAddParam addParam = createTestDeviceVoiceAddParam();
        VoicePacket voicePacket = new VoicePacket();
        voicePacket.setId(1L);
        VoiceWork voiceWork = new VoiceWork();
        voiceWork.setId(1L);

        // Mock OSS服务调用
        File mockFile = new File("test.mp3");
        when(ossService.getObjectFile(null, addParam.getUrl())).thenReturn(mockFile);
        when(ossService.putFileToName(eq(null), eq(mockFile), eq("voice"), anyString()))
                .thenReturn("voice-url.mp3");
        when(ossService.putFileToName(eq(null), eq(mockFile), eq("device"), anyString()))
                .thenReturn("device-voice-url.mp3");

        when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(voicePacket);
        when(workRepository.save(any(VoiceWork.class))).thenReturn(voiceWork);
        when(deviceVoiceRepository.saveAll(anyList())).thenReturn(Arrays.asList(new DeviceVoice()));
        when(deviceVoiceRepository.findByDeviceIdAndTitleAndDelStatus(anyLong(), anyString(), eq(0)))
                .thenReturn(new ArrayList<>());

        ResultBean<Boolean> addVoiceResult = deviceService.addDeviceVoice(addParam);
        assertTrue(addVoiceResult.isOk());

        // 4. 解绑设备
        when(deviceRepository.findById(1L)).thenReturn(Optional.of(newDevice));

        ResultBean<Boolean> unbindResult = deviceService.unBind(1L);
        assertTrue(unbindResult.isOk());

        // 验证所有操作都被调用
        verify(shopService).createShop(1L);
        verify(deviceRepository, atLeast(2)).save(any(Device.class));
        verify(voicePacketRepository, times(2)).save(any(VoicePacket.class));
        verify(workRepository).save(any(VoiceWork.class));
        verify(deviceVoiceRepository).saveAll(anyList());
        verify(remoteDeviceService).sendAudio(anyLong(), anyString(), anyLong(), anyString());
    }

    @Test
    void 测试并发设备操作_多个解绑操作() {
        // 测试并发操作场景
        Device device = createTestDevice();
        device.setId(1L);

        when(deviceRepository.findById(1L)).thenReturn(Optional.of(device));
        when(deviceRepository.save(any(Device.class))).thenReturn(device);

        // 模拟多个并发解绑操作
        ResultBean<Boolean> result1 = deviceService.unBind(1L);
        ResultBean<Boolean> result2 = deviceService.unBind(1L);
        ResultBean<Boolean> result3 = deviceService.unBind(1L);

        assertTrue(result1.isOk());
        assertTrue(result2.isOk());
        assertTrue(result3.isOk());

        verify(deviceRepository, times(3)).findById(1L);
        verify(deviceRepository, times(3)).save(any(Device.class));
    }

    @Test
    void 测试批量设备语音操作_五个设备同时添加语音() {
        // 测试批量语音操作
        DeviceVoiceAddParam addParam = createTestDeviceVoiceAddParam();
        addParam.setDeviceIds(Arrays.asList(1L, 2L, 3L, 4L, 5L)); // 5个设备

        VoicePacket voicePacket = new VoicePacket();
        voicePacket.setId(1L);
        VoiceWork voiceWork = new VoiceWork();
        voiceWork.setId(1L);

        // Mock OSS服务调用
        File mockFile = new File("test.mp3");
        when(ossService.getObjectFile(null, addParam.getUrl())).thenReturn(mockFile);
        when(ossService.putFileToName(eq(null), eq(mockFile), eq("voice"), anyString()))
                .thenReturn("voice-url.mp3");
        when(ossService.putFileToName(eq(null), eq(mockFile), eq("device"), anyString()))
                .thenReturn("device-voice-url.mp3");

        when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(voicePacket);
        when(workRepository.save(any(VoiceWork.class))).thenReturn(voiceWork);
        when(deviceVoiceRepository.saveAll(anyList())).thenReturn(Arrays.asList(new DeviceVoice()));
        when(deviceVoiceRepository.findByDeviceIdAndTitleAndDelStatus(anyLong(), anyString(), eq(0)))
                .thenReturn(new ArrayList<>());

        ResultBean<Boolean> result = deviceService.addDeviceVoice(addParam);

        assertTrue(result.isOk());
        verify(remoteDeviceService, times(5)).sendAudio(anyLong(), anyString(), anyLong(), anyString());
        verify(deviceVoiceRepository).saveAll(argThat(list -> ((List<?>) list).size() == 5));
    }

    @Test
    void 测试设备语音更新_包含OSS操作的完整流程() {
        // 测试设备语音更新涉及OSS操作的完整流程
        DeviceVoiceVo voiceVo = createTestDeviceVoiceVo();
        DeviceVoice existingVoice = new DeviceVoice();
        existingVoice.setId(1L);

        File mockFile = new File("test.mp3");

        when(deviceVoiceRepository.findById(1L)).thenReturn(Optional.of(existingVoice));
        when(ossService.getObjectFile(null, "test-voice.mp3")).thenReturn(mockFile);
        when(ossService.putFile(null, mockFile, "voice")).thenReturn("new-voice-url.mp3");
        when(deviceVoiceRepository.save(any(DeviceVoice.class))).thenReturn(existingVoice);

        ResultBean<Boolean> result = deviceService.updateDeviceVoice(voiceVo);

        assertTrue(result.isOk());
        verify(ossService).getObjectFile(null, "test-voice.mp3");
        verify(ossService).deleteObject(null, "test-voice.mp3");
        verify(ossService).putFile(null, mockFile, "voice");
        verify(deviceVoiceRepository).save(argThat(dv -> "new-voice-url.mp3".equals(dv.getVoiceUrl())));
    }

    @Test
    void 测试复杂设备查询_多条件分页查询() {
        // 测试复杂的设备查询场景
        DeviceQueryVo queryVo = new DeviceQueryVo();
        queryVo.setShopIds(Arrays.asList(1L, 2L, 3L));
        queryVo.setCreateId(1L);
        queryVo.setKeyword("测试设备");
        queryVo.setStatus(1);
        queryVo.setBindStatus(BindStatusEnum.BINDING.ordinal());
        queryVo.setPageSize(20);
        queryVo.setPageNumber(2);

        List<Device> devices = Arrays.asList(createTestDevice(), createTestDevice());
        org.springframework.data.domain.Page<Device> page = new org.springframework.data.domain.PageImpl<>(devices,
                org.springframework.data.domain.PageRequest.of(1, 20), 100);

        when(deviceRepository.findAll(any(org.springframework.data.jpa.domain.Specification.class),
                any(org.springframework.data.domain.Pageable.class))).thenReturn(page);

        ResultBean<Map<String, Object>> result = deviceService.list(queryVo);

        assertTrue(result.isOk());
        Map<String, Object> data = result.getResultData();
        assertEquals(100, data.get("count"));
        assertEquals(devices, data.get("result"));
    }

    @Test
    void 测试错误恢复场景_数据库异常时的回滚处理() {
        // 测试错误恢复场景
        DeviceVoiceAddParam addParam = createTestDeviceVoiceAddParam();

        // 模拟语音包保存失败
        when(voicePacketRepository.save(any(VoicePacket.class)))
                .thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> deviceService.addDeviceVoice(addParam));

        // 验证没有进行后续操作
        verify(workRepository, never()).save(any(VoiceWork.class));
        verify(deviceVoiceRepository, never()).saveAll(anyList());
        verify(remoteDeviceService, never()).sendAudio(anyLong(), anyString(), anyLong(), anyString());
    }

    // Helper methods
    private Device createTestDevice() {
        Device device = new Device();
        device.setName("Test Device");
        device.setSn("TEST001");
        device.setUserId(1L);
        device.setVolume(50);
        device.setStatus(1);
        device.setBindStatus(BindStatusEnum.UNBIND.ordinal());
        device.setDelStatus(0);
        device.setCreateTime(new Date());
        return device;
    }

    private DeviceVoiceAddParam createTestDeviceVoiceAddParam() {
        DeviceVoiceAddParam param = new DeviceVoiceAddParam();
        param.setDeviceIds(Arrays.asList(1L));
        param.setTitle("Test Voice");
        param.setContent("Test content");
        param.setVoiceId(1L);
        param.setBackgroundMusicId(1L);
        param.setBackgroundMusicVolume(30);
        param.setShopId(1L);
        param.setUserId(1L);
        param.setUrl("test-voice-url.mp3");
        param.setTime(10);
        param.setSpeed(50);
        param.setVolume(50);
        param.setPitch(50);
        param.setType(1);
        return param;
    }

    private DeviceVoiceVo createTestDeviceVoiceVo() {
        DeviceVoiceVo vo = new DeviceVoiceVo();
        vo.setId(1L);
        vo.setDeviceId(1L);
        vo.setSpeed(50);
        vo.setVolume(50);
        vo.setPitch(50);
        vo.setBackgroundMusicVolume(30);
        vo.setVoiceUrl("test-voice.mp3");
        return vo;
    }
}
